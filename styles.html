<!DOCTYPE html>
<html lang="en" class="no-js" >
<head>

    <!--- basic page needs
    ================================================== -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Styles - Lounge</title>

    <script>
        document.documentElement.classList.remove('no-js');
        document.documentElement.classList.add('js');
    </script>

    <!-- CSS
    ================================================== -->
    <link rel="stylesheet" href="css/vendor.css">
    <link rel="stylesheet" href="css/styles.css">

    <style>
        .s-pageheader {
            padding-top: var(--vspace-3);
        }
        .s-pagecontent {
            /* padding-bottom: var(--vspace-4); */
        }
    </style>

    <!-- favicons
    ================================================== -->
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="manifest" href="site.webmanifest">

</head>


<body id="top">
    
    <!-- preloader
    ================================================== -->
    <div id="preloader">
        <div id="loader" class="dots-fade">
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>   

    <!-- page wrap
    ================================================== -->
    <div id="page" class="s-pagewrap">

        
        <!-- # site header 
        ================================================== -->
        <header class="s-header">

            <div class="container s-header__content">
                
                <div class="s-header__block">
                    <div class="header-logo">
                        <a class="logo" href="index.html">
                            <img src="images/logo.svg" alt="Homepage">
                        </a>
                    </div>
                    <a class="header-menu-toggle" href="#0"><span>Menu</span></a>
                </div> <!-- end s-header__block -->
            
                <nav class="header-nav">    
                    <ul class="header-nav__links">
                        <li class="current"><a class="smoothscroll" href="#intro">Intro</a></li>
                        <li><a class="smoothscroll" href="#about">About</a></li>
                        <li><a class="smoothscroll" href="#menu">Menu</a></li>
                        <li><a class="smoothscroll" href="#gallery">Gallery</a></li>
                    </ul> <!-- end header-nav__links -->  
                    
                    <div class="header-contact">
                        <a href="tel:+" class="header-contact__num btn">
                            <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke-width="1.5" width="24" height="24" color="#000000"><defs><style>.cls-6376396cc3a86d32eae6f0dc-1{fill:none;stroke:currentColor;stroke-miterlimit:10;}</style></defs><path class="cls-6376396cc3a86d32eae6f0dc-1" d="M19.64,21.25c-2.54,2.55-8.38.83-13-3.84S.2,6.9,2.75,4.36L5.53,1.57,10.9,6.94l-2,2A2.18,2.18,0,0,0,8.9,12L12,15.1a2.18,2.18,0,0,0,3.07,0l2-2,5.37,5.37Z"></path></svg>
                            ************
                        </a>
                    </div> <!-- end header-contact -->
                </nav> <!-- end header-nav -->         
            
            </div> <!-- end s-header__content -->

        </header> <!-- end s-header -->

        
        <!-- # main content
        ================================================== -->
        <article class="s-content container">

            <!-- ## pageheader -->
            <div class="row s-pageheader row-x-center">
                <div class="column xl-8 lg-10 md-12 s-pageheader__content u-text-center">
                    <h1>                        
                        Style Guide
                    </h1> 
                    
                    <p class="lead">Lorem ipsum Officia elit ad tempor dolore est ex incididunt incididunt occaecat culpa deserunt 
                    sunt labore in cillum ullamco magna in Excepteur consequat in reprehenderit proident mollit incididunt officia commodo.
                    Duis ea officia sed dolor pariatur enim dolore dolore quis incididunt nulla exercitation commodo veniam et ea incididunt.
                    </p>

                    <hr>
                </div>
            </div> <!-- end s-pageheader -->
            
            <!-- ## pagecontent -->
            <section class="s-pagecontent">                
        
                <div class="row">
        
                    <div class="column xl-6 md-12">
        
                        <h3>Paragraph and Image</h3>
        
                        <p><a href="#"><img width="120" height="120" class="u-pull-left" alt="sample-image" src="images/sample-image.jpg"></a>
                        Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Donec libero. Suspendisse bibendum.Cras id urna. Morbi 
                        tincidunt, orci ac convallis aliquam, lectus turpis varius lorem, eu posuere nunc justo tempus leo. Donec mattis, 
                        purus nec placerat bibendum, dui pede condimentum odio, ac blandit ante orci ut diam. Cras fringilla magna. 
                        Phasellus suscipit, leo a pharetra condimentum, lorem tellus eleifend magna, eget fringilla velit magna id 
                        neque posuere nunc justo tempus leo. </p>
        
                        <p>
                        Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Donec libero. Suspendisse bibendum. Cras id urna. 
                        Morbi tincidunt, orci ac convallis aliquam, lectus turpis varius lorem, eu posuere nunc justo tempus leo. 
                        Donec mattis, purus nec placerat bibendum, dui pede condimentumodio, ac blandit ante orci ut diam.	
                        </p>
        
                        <p>A <a href="#">link</a>,
                        <abbr title="this really isn't a very good description">abbrebation</abbr>,
                        <strong>strong text</strong>,
                        <em>em text</em>,
                        <del>deleted text</del>,                         
                        <code>.code</code>, and
                        <mark>this is a mark text.</mark>
                        </p>
        
                    </div>
        
                    <div class="column xl-6 md-12">
        
                        <h3>Drop Caps</h3>
        
                        <p class="drop-cap">Far far away, behind the word mountains, far from the countries Vokalia and Consonantia,
                        there live the blind texts. Separated they live in Bookmarksgrove right at the coast of the
                        Semantics, a large language ocean. A small river named Duden flows by their place and supplies it with the 
                        necessary regelialia. Morbi tincidunt, orci ac convallis aliquam, lectus turpis varius lorem, 
                        euposuere nunc justo tempus leo. Donec mattis, purus nec placerat bibendum, dui pede condimentum odio, 
                        ac blandit ante orci ut diam. Cras fringilla magna. Phasellus suscipit, leo a pharetra condimentum, 
                        lorem tellus eleifend magna, eget fringilla velit magna id neque.
                        </p>
        
                        <h3>Small Print</h3>
        
                        <p><small>Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Donec libero.</small></p>                    
                 
                    </div>
        
                </div> <!-- end row -->
        
                <div class="row">
        
                    <div class="column xl-6 md-12">
        
                        <h3 class="u-add-bottom">Pull Quotes</h3>
        
                        <p>
                        Perspiciatis nemo unde et nobis modi consequatur officia amet. Ut enim 
                        tenetur provident maiores. Perspiciatis asperiores incidunt sequi 
                        quisquam. Enim aut. 
                        </p>
        
                        <figure class="pull-quote">
                            <blockquote>                                

                                <p>
                                For God so loved the world, that he gave his only Son, that whoever believes in 
                                him should not perish but have eternal life. For God did not send his Son into 
                                the world to condemn the world, but in order that the world might be 
                                saved through him.
                                </p>
            
                                <footer>
                                    <cite>
                                        John 3:16-17 ESV
                                    </cite>
                                </footer>
                            </blockquote>
                        </figure>        
        
                    </div>
        
                    <div class="column xl-6 md-12">
        
                        <h3 class="h-add-bottom">Block Quotes</h3>
        
                        <blockquote cite="http://where-i-got-my-info-from.com">
                            <p>
                            If we find ourselves with a desire that nothing in this world can satisfy, 
                            the most probable explanation is that we were made for another world.
                            </p>
    
                            <footer>
                                <cite>
                                    <a href="#0">C. S. Lewis</a>
                                </cite>
                            </footer>        
                        </blockquote>
        
                        <blockquote>
                            <p>There is a God-shaped vacuum in the heart of each man which cannot be satisfied 
                            by any created thing but only by God the Creator.</p>
        
                            <footer>
                                <cite>Blaise Pascal</cite>
                            </footer>        
                        </blockquote>
        
                    </div>
        
                </div> <!-- end row -->
        
                <div class="row u-add-half-bottom">
        
                    <div class="column xl-6 md-12">
        
                        <h3>Example Lists</h3>
        
                        <ol>
                            <li>Here is an example</li>
                            <li>of an ordered list.</li>
                            <li>A parent list item.
                                <ul>
                                    <li>one</li>
                                    <li>two</li>
                                    <li>three</li>
                                </ul>
                                </li>
                                <li>A list item.</li>
                            </ol>
        
                        <ul class="disc">
                            <li>Here is an example</li>
                            <li>of an unordered list.</li>
                        </ul>
        
                        <h3>Definition Lists</h3>
        
                        <h5>a) Multi-line Definitions (default) </h5>
        
                        <dl class="dictionary-style">
                            <dt><strong>This is a term</strong></dt>
                                <dd>this is the definition of that term, which both live in a <code>dl</code>.</dd>
                            <dt><strong>Another Term</strong></dt>
                                <dd>And it gets a definition too, which is this line</dd>
                                <dd>This is a 2<sup>nd</sup> definition for a single term. A <code>dt</code> may be followed by multiple <code>dd</code>s.</dd>
                        </dl>
        
                        <h3 class="u-add-bottom">Skill Bars</h3>
        
                        <ul class="skill-bars">
                            <li>
                            <div class="progress percent90"><span>90%</span></div>
                            <strong>HTML</strong>
                            </li>
                            <li>
                            <div class="progress percent85"><span>85%</span></div>
                            <strong>CSS</strong>
                            </li>
                            <li>
                            <div class="progress percent70"><span>70%</span></div>
                            <strong>Javascript</strong>
                            </li>
                            <li>
                            <div class="progress percent95"><span>95%</span></div>
                            <strong>PHP</strong>
                            </li>
                            <li>
                            <div class="progress percent75"><span>75%</span></div>
                            <strong>Illustrator</strong>
                            </li>
                            <li>
                            <div class="progress percent90"><span>90%</span></div>
                            <strong>Figma</strong>
                            </li>
                        </ul>
        
                    </div>
        
                    <div class="column xl-6 md-12">
        
                        <h3 class="u-add-bottom">Buttons</h3>
        
                        <p>
                            <a class="btn" href="#0">Default Button</a>
                            <a class="btn btn--primary" href="#0">Primary Button</a>
                            <a class="btn btn--ghost" href="#0">Ghost Button</a>
                            <a class="btn btn--small" href="#0">Small Button</a>
                            <a class="btn btn--medium" href="#0">Medium Button</a>
                            <a class="btn btn--large" href="#0">Large Button</a>  
                        </p>
        
                        <h3>Stats Tabs</h3>
        
                        <ul class="stats-tabs">
                            <li><a href="#0">1,234 <em>Peter</em></a></li>
                            <li><a href="#0">567 <em>James</em></a></li>
                            <li><a href="#0">23,456 <em>John</em></a></li>
                            <li><a href="#0">3,456 <em>Andrew</em></a></li>
                            <li><a href="#0">456 <em>Philip</em></a></li>
                            <li><a href="#0">26 <em>Matthew</em></a></li>
                        </ul>
        
                        <h3 class="u-add-bottom">Code</h3>
        
<pre><code class="language-css">
    code {
    font-size: 1.4rem;
    margin: 0 .2rem;
    padding: .2rem .6rem;
    white-space: nowrap;
    background: #F1F1F1;
    border: 1px solid #E1E1E1;	
    border-radius: 3px;
    }
</code></pre>
        
                    </div>
        
                </div> <!-- end row -->
        
                <div class="row u-add-half-bottom">
        
                    <div class="column xl-6 md-12">
                            
                        <h1>H1 Heading Doloremque dolor voluptas est sequi omnis.</h1>
                        <p>Doloremque dolor voluptas est sequi omnis. Pariatur ut aut. Sed enim tempora qui veniam qui cum vel. 
                        Voluptas odit at vitae minima. In assumenda ut. Voluptatem totam impedit accusantium reiciendis excepturi aut qui accusamus praesentium.</p>
        
                        <h2>H2 Heading Doloremque dolor voluptas est sequi omnis.</h2>
                        <p>Doloremque dolor voluptas est sequi omnis. Pariatur ut aut. Sed enim tempora qui veniam qui cum vel. 
                        Voluptas odit at vitae minima. In assumenda ut. Voluptatem totam impedit accusantium reiciendis excepturi aut qui accusamus praesentium.</p>
        
                        <h3>H3 Heading Doloremque dolor voluptas est sequi omnis.</h3>
                        <p>Doloremque dolor voluptas est sequi omnis. Pariatur ut aut. Sed enim tempora qui veniam qui cum vel. 
                        Voluptas odit at vitae minima. In assumenda ut. Voluptatem totam impedit accusantium reiciendis excepturi aut qui accusamus praesentium.</p>
                                
                    </div>
        
                    <div class="column xl-6 md-12">
                        <h4>H4 Heading Doloremque dolor voluptas est sequi omnis.</h4>
                        <p>Doloremque dolor voluptas est sequi omnis. Pariatur ut aut. Sed enim tempora qui veniam qui cum vel. 
                        Voluptas odit at vitae minima. In assumenda ut. Voluptatem totam impedit accusantium reiciendis excepturi aut qui accusamus praesentium.</p>
        
                        <h5>H5 Heading Doloremque dolor voluptas est sequi omnis.</h5>
                        <p>Doloremque dolor voluptas est sequi omnis. Pariatur ut aut. Sed enim tempora qui veniam qui cum vel. 
                        Voluptas odit at vitae minima. In assumenda ut. Voluptatem totam impedit accusantium reiciendis excepturi aut qui accusamus praesentium.</p>
        
                        <h6>H6 Heading Doloremque dolor voluptas est sequi omnis.</h6>
                        <p>Doloremque dolor voluptas est sequi omnis. Pariatur ut aut. Sed enim tempora qui veniam qui cum vel. 
                        Voluptas odit at vitae minima. In assumenda ut. Voluptatem totam impedit accusantium reiciendis excepturi aut qui accusamus praesentium.</p>
    
                    </div>
        
                </div> <!-- end row -->
        
                <div class="row u-add-half-bottom">
        
                    <div class="column xl-6 md-12">        
                        <h3 class="u-add-bottom">Responsive Image</h3>
        
                        <figure>
                            <img src="images/wheel-500.jpg" 
                                 srcset="images/wheel-1000.jpg 1000w, 
                                 images/wheel-500.jpg 500w" 
                                 sizes="(max-width: 1000px) 100vw, 1000px" alt="">
        
                            <figcaption>
                                Here is some random picture.
                            </figcaption>
                        </figure>        
                    </div>
        
                    <div class="column xl-6 md-12">        
                        <h3 class="u-add-bottom">Responsive video</h3>
        
                        <div class="video-container">
                        <!-- <iframe src="https://player.vimeo.com/video/14592941?color=00a650&title=0&byline=0&portrait=0" width="500" height="281" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>  -->
                            <iframe width="560" height="315" src="https://www.youtube.com/embed/tfxgjwLSq_E?si=irgF6oGjYZfMnIiN&amp;controls=0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                        </div>        
                    </div>
                    
                </div> <!-- end row -->
        
                <div class="row u-add-bottom">
        
                    <div class="column xl-12">
        
                        <h3>Tables</h3>
                        <p>Be sure to use properly formed table markup with <code>&lt;thead&gt;</code> and <code>&lt;tbody&gt;</code> when building a <code>table</code>.</p>
        
                        <div class="table-responsive">
        
                            <table>
                                    <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Age</th>
                                        <th>Sex</th>
                                        <th>Location</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>William J. Seymour</td>
                                        <td>34</td>
                                        <td>Male</td>
                                        <td>Azusa Street</td>
                                    </tr>
                                    <tr>
                                        <td>Jennie Evans Moore</td>
                                        <td>30</td>
                                        <td>Female</td>
                                        <td>Azusa Street</td>
                                    </tr>
                                    </tbody>
                            </table>
        
                        </div>
        
                    </div>
                    
                </div> <!-- end row -->
        
                <div class="row">
        
                    <div class="column xl-12">
                        <h3>Pagination</h3>
        
                        <nav class="pgn">
                            <ul>
                                <li>
                                    <a class="pgn__prev" href="#0">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M12.707 17.293L8.414 13H18v-2H8.414l4.293-4.293-1.414-1.414L4.586 12l6.707 6.707z"/></svg>
                                    </a>
                                </li>
                                <li><a class="pgn__num" href="#0">1</a></li>
                                <li><span class="pgn__num current">2</span></li>
                                <li><a class="pgn__num" href="#0">3</a></li>
                                <li><a class="pgn__num" href="#0">4</a></li>
                                <li><a class="pgn__num" href="#0">5</a></li>
                                <li><span class="pgn__num dots">…</span></li>
                                <li><a class="pgn__num" href="#0">8</a></li>
                                <li>
                                    <a class="pgn__next" href="#0">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M11.293 17.293l1.414 1.414L19.414 12l-6.707-6.707-1.414 1.414L15.586 11H6v2h9.586z"/></svg>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
        
                </div> <!-- end row -->
        
                <div class="row">
        
                    <div class="column xl-6 md-12">
        
                        <h3 class="u-add-bottom">Form Styles</h3>
        
                        <form>
                            <div>
                                <label for="sampleInput">Your email</label>
                                <input class="u-fullwidth" type="email" placeholder="<EMAIL>" id="sampleInput">
                            </div>
                            <div>
                                <label for="sampleRecipientInput">Reason for contacting</label>
                                <div class="ss-custom-select">
                                    <select class="u-fullwidth" id="sampleRecipientInput">
                                        <option value="Option 1">Questions</option>
                                        <option value="Option 2">Report</option>
                                        <option value="Option 3">Others</option>
                                    </select>
                                </div>
                            </div>
                            
                            <label for="exampleMessage">Message</label>
                            <textarea class="u-fullwidth" placeholder="Your message" id="exampleMessage"></textarea>
        
                            <label class="u-add-bottom">
                                <input type="checkbox">
                                <span class="label-text">Send a copy to yourself</span>
                            </label>
                        
                            <input class="btn--primary u-fullwidth" type="submit" value="Submit">
                        </form>
        
                    </div>
        
                    <div class="column xl-6 md-12">
        
                        <h3>Alert Boxes</h3>
        
                        <br>
                        
                        <div class="alert-box alert-box--error">
                            <p>Error Message. Your Message Goes Here.</p>
                            <span class="alert-box__close"></span>
                        </div><!-- end error -->
                                
                        <div class="alert-box alert-box--success">
                            <p>Success Message. Your Message Goes Here.</p>
                            <span class="alert-box__close"></span>
                        </div><!-- end success -->
                                
                        <div class="alert-box alert-box--info">
                            <p>Info Message. Your Message Goes Here.</p>
                            <span class="alert-box__close"></span>
                        </div><!-- end info -->
                                
                        <div class="alert-box alert-box--notice">
                            <p>Notice Message. Your Message Goes Here.</p>
                            <span class="alert-box__close"></span>
                        </div><!-- end notice -->
                    
                    </div>
        
                </div> <!-- end row -->
    
                <div class="row">
                
                    <div class="xl-12 column">
                        <h3>Grid Columns</h3>
                    </div>
        
                </div> <!-- Row End-->
        
                <div class="row">
        
                    <div class="xl-4 md-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. 
                        </p>
                    </div>
        
                    <div class="xl-4 md-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. 
                        </p>
                    </div>
        
                    <div class="xl-4 md-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. 
                        </p>
                    </div>
        
                </div>
        
                <div class="row">
        
                    <div class="xl-3 md-6 tab-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. 
                        </p>
                    </div>
        
                    <div class="xl-3 md-6 tab-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. 
                        </p>
                    </div>
        
                    <div class="xl-3 md-6 tab-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. 
                        </p>
                    </div>
        
                    <div class="xl-3 md-6 tab-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. 
                        </p>
                    </div>
        
                </div>
        
                <div class="row">
        
                    <div class="xl-6 tab-12 column">
                        <div class="row">
                            <div class="column xl-6 md-12">
                                <p>
                                    Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                                    nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus dictum nunc ligula ipsum. 
                                    </p>
                            </div>
                            <div class="column xl-6 md-12">
                                <p>
                                    Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                                    nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus dapibus ligula ipsum. 
                                    </p>
                            </div>
                        </div>                
                    </div>
        
                    <div class="xl-6 tab-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. 
                        </p>
                    </div>
        
                </div>
        
                <div class="row">
        
                    <div class="xl-8 md-7 tab-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor.
                        </p>
                    </div>
        
                    <div class="xl-4 md-5 tab-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. 
                        </p>
                    </div>
        
                </div>
        
                <div class="row">
        
                    <div class="xl-3 md-5 tab-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at. 
                        </p>
                    </div>
        
                    <div class="xl-9 md-7 tab-12 column">
                        <p>
                        Cras aliquet. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor.Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor. Integer faucibus, eros ac molestie placerat, enim tellus varius lacus,
                        nec dictum nunc tortor id urna. Suspendisse dapibus ullamcorper pede. Vivamus ligula ipsum,
                        faucibus at, tincidunt eget, porttitor non, dolor.
                        </p>
                    </div>
        
                </div> 

                <hr class="fancy">

            </section> <!-- pagecontent --> 

        </article> <!-- end main content -->


        <!-- # footer 
        ================================================== -->
        <footer id="footer" class="container s-footer">           
              
            <div class="row s-footer__main">             
                <div class="column xl-3 lg-12 footer-block s-footer__main-start">     

                    <div class="s-footer__logo">
                        <a class="logo" href="index.html">
                            <img src="images/logo.svg" alt="Homepage">
                        </a>
                    </div>  

                    <ul class="s-footer__social social-list">
                        <li>
                            <a href="#0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="fill:rgba(0, 0, 0, 1);transform:;-ms-filter:"><path d="M20,3H4C3.447,3,3,3.448,3,4v16c0,0.552,0.447,1,1,1h8.615v-6.96h-2.338v-2.725h2.338v-2c0-2.325,1.42-3.592,3.5-3.592 c0.699-0.002,1.399,0.034,2.095,0.107v2.42h-1.435c-1.128,0-1.348,0.538-1.348,1.325v1.735h2.697l-0.35,2.725h-2.348V21H20 c0.553,0,1-0.448,1-1V4C21,3.448,20.553,3,20,3z"></path></svg>
                                <span class="u-screen-reader-text">Facebook</span>
                            </a>
                        </li>
                        <li>
                            <a href="#0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="fill: rgba(0, 0, 0, 1);transform: ;msFilter:;"><path d="m20.665 3.717-17.73 6.837c-1.21.486-1.203 1.161-.222 1.462l4.552 1.42 10.532-6.645c.498-.303.953-.14.579.192l-8.533 7.701h-.002l.002.001-.314 4.692c.46 0 .663-.211.921-.46l2.211-2.15 4.599 3.397c.848.467 1.457.227 1.668-.785l3.019-14.228c.309-1.239-.473-1.8-1.282-1.434z"></path></svg>
                                <span class="u-screen-reader-text">Telegram</span>
                            </a>
                        </li>
                        <li>
                            <a href="#0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="fill:rgba(0, 0, 0, 1);transform:;-ms-filter:"><path d="M11.999,7.377c-2.554,0-4.623,2.07-4.623,4.623c0,2.554,2.069,4.624,4.623,4.624c2.552,0,4.623-2.07,4.623-4.624 C16.622,9.447,14.551,7.377,11.999,7.377L11.999,7.377z M11.999,15.004c-1.659,0-3.004-1.345-3.004-3.003 c0-1.659,1.345-3.003,3.004-3.003s3.002,1.344,3.002,3.003C15.001,13.659,13.658,15.004,11.999,15.004L11.999,15.004z"></path><circle cx="16.806" cy="7.207" r="1.078"></circle><path d="M20.533,6.111c-0.469-1.209-1.424-2.165-2.633-2.632c-0.699-0.263-1.438-0.404-2.186-0.42 c-0.963-0.042-1.268-0.054-3.71-0.054s-2.755,0-3.71,0.054C7.548,3.074,6.809,3.215,6.11,3.479C4.9,3.946,3.945,4.902,3.477,6.111 c-0.263,0.7-0.404,1.438-0.419,2.186c-0.043,0.962-0.056,1.267-0.056,3.71c0,2.442,0,2.753,0.056,3.71 c0.015,0.748,0.156,1.486,0.419,2.187c0.469,1.208,1.424,2.164,2.634,2.632c0.696,0.272,1.435,0.426,2.185,0.45 c0.963,0.042,1.268,0.055,3.71,0.055s2.755,0,3.71-0.055c0.747-0.015,1.486-0.157,2.186-0.419c1.209-0.469,2.164-1.424,2.633-2.633 c0.263-0.7,0.404-1.438,0.419-2.186c0.043-0.962,0.056-1.267,0.056-3.71s0-2.753-0.056-3.71C20.941,7.57,20.801,6.819,20.533,6.111z M19.315,15.643c-0.007,0.576-0.111,1.147-0.311,1.688c-0.305,0.787-0.926,1.409-1.712,1.711c-0.535,0.199-1.099,0.303-1.67,0.311 c-0.95,0.044-1.218,0.055-3.654,0.055c-2.438,0-2.687,0-3.655-0.055c-0.569-0.007-1.135-0.112-1.669-0.311 c-0.789-0.301-1.414-0.923-1.719-1.711c-0.196-0.534-0.302-1.099-0.311-1.669c-0.043-0.95-0.053-1.218-0.053-3.654 c0-2.437,0-2.686,0.053-3.655c0.007-0.576,0.111-1.146,0.311-1.687c0.305-0.789,0.93-1.41,1.719-1.712 c0.534-0.198,1.1-0.303,1.669-0.311c0.951-0.043,1.218-0.055,3.655-0.055c2.437,0,2.687,0,3.654,0.055 c0.571,0.007,1.135,0.112,1.67,0.311c0.786,0.303,1.407,0.925,1.712,1.712c0.196,0.534,0.302,1.099,0.311,1.669 c0.043,0.951,0.054,1.218,0.054,3.655c0,2.436,0,2.698-0.043,3.654H19.315z"></path></svg>
                                <span class="u-screen-reader-text">Instagram</span>
                            </a>
                        </li>
                        <li>
                            <a href="#0">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" style="fill: rgba(0, 0, 0, 1);transform: ;msFilter:;"><path d="M8.31 10.28a2.5 2.5 0 1 0 2.5 2.49 2.5 2.5 0 0 0-2.5-2.49zm0 3.8a1.31 1.31 0 1 1 0-2.61 1.31 1.31 0 1 1 0 2.61zm7.38-3.8a2.5 2.5 0 1 0 2.5 2.49 2.5 2.5 0 0 0-2.5-2.49zM17 12.77a1.31 1.31 0 1 1-1.31-1.3 1.31 1.31 0 0 1 1.31 1.3z"></path><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm7.38 10.77a3.69 3.69 0 0 1-6.2 2.71L12 16.77l-1.18-1.29a3.69 3.69 0 1 1-5-5.44l-1.2-1.3H7.3a8.33 8.33 0 0 1 9.41 0h2.67l-1.2 1.31a3.71 3.71 0 0 1 1.2 2.72z"></path><path d="M14.77 9.05a7.19 7.19 0 0 0-5.54 0A4.06 4.06 0 0 1 12 12.7a4.08 4.08 0 0 1 2.77-3.65z"></path></svg>
                                <span class="u-screen-reader-text">Tripadvisor</span>
                            </a>
                        </li>
                    </ul> <!--end s-footer__social -->

                </div> <!-- end s-footer__main-start -->
                

                <div class="column xl-9 lg-12 s-footer__main-end grid-cols grid-cols--wrap">

                    <div class="grid-cols__column footer-block">
                        <h6>Location</h6>
                        <p>
                        456 Elm Street, Los Angeles <br>
                        CA 90001
                        </p>
                    </div>
                    
                    <div class="grid-cols__column footer-block">     
                        <h6>Contacts</h6>
                        <ul class="link-list">
                            <li><a href="mailto:#0"><EMAIL></a></li>
                            <li><a href="tel:+2135551212">(213) ************</a></li>
                        </ul> 
                    </div>
                    
                    <div class="grid-cols__column footer-block">                   
                        <h6>Opening Hours</h6>
                        <ul class="opening-hours">
                            <li><span class="opening-hours__days">Weekdays</span><span class="opening-hours__time">10:00am - 9:00pm</span></li>
                            <li><span class="opening-hours__days">Weekends</span><span class="opening-hours__time">9:00am - 10:00pm</span></li>
                        </ul> 
                    </div>  

                </div> <!-- s-footer__main-end -->                  

            </div> <!-- end  s-footer__main-content -->                 
            
            <div class="row s-footer__bottom">       
                
                <div class="column xl-6 lg-12">
                    <p class="ss-copyright">
                        <span>© Lounge 2025</span> 
                        <span>Design by <a href="https://styleshout.com/">StyleShout</a></span>

                        Distributed by <a href="https://themewagon.com" target="_blank">ThemeWagon</a>
                    </p>
                </div>

            </div> <!-- end s-footer__bottom -->          

            <div class="ss-go-top">
                <a class="smoothscroll" title="Back to Top" href="#top">                 
                    <svg clip-rule="evenodd" fill-rule="evenodd" stroke-linejoin="round" stroke-miterlimit="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="m14.523 18.787s4.501-4.505 6.255-6.26c.146-.146.219-.338.219-.53s-.073-.383-.219-.53c-1.753-1.754-6.255-6.258-6.255-6.258-.144-.145-.334-.217-.524-.217-.193 0-.385.074-.532.221-.293.292-.295.766-.004 1.056l4.978 4.978h-14.692c-.414 0-.75.336-.75.75s.336.75.75.75h14.692l-4.979 4.979c-.289.289-.286.762.006 *************.341.222.533.222.19 0 .378-.072.522-.215z" fill-rule="nonzero"/></svg>
                </a>                                
                <span>Back To Top</span>   
            </div> <!-- end ss-go-top -->
            
        </footer> <!-- end s-footer -->

    </div> <!-- end page-wrap -->


    <!-- Java Script
    ================================================== -->
    <script src="js/plugins.js"></script>
    <script src="js/main.js"></script>

</body>
</html>