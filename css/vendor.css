/* ===================================================================
 * Lounge Third-party Stylesheets
 * Template Ver. 1.0.0
 * 05-03-2025
 * -------------------------------------------------------------------
 *
 * TOC:
 * # PrismJS
 * # Swiper
 * # GLightbox
 *
 * ------------------------------------------------------------------- */

/* ===================================================================
 * # PrismJS 1.20.0
 *   https://prismjs.com/download.html#themes=prism-okaidia&languages=markup+css+clike+javascript+markup-templating+php 
 *   
 *   okaidia theme for JavaScript, CSS and HTML
 *   Loosely based on Monokai textmate theme by http://www.monokai.nl/
 *   <AUTHOR>
 * ------------------------------------------------------------------- */
code[class*=language-],
pre[class*=language-] {
    color          : #f8f8f2;
    background     : none;
    text-shadow    : 0 1px rgba(0, 0, 0, 0.3);
    font-family    : var(--font-mono);
    font-size      : var(--text-sm);
    text-align     : left;
    white-space    : pre;
    word-spacing   : normal;
    word-break     : normal;
    word-wrap      : normal;
    line-height    : var(--vspace-1);
    -moz-tab-size  : 4;
    -o-tab-size    : 4;
    tab-size       : 4;
    -webkit-hyphens: none;
    hyphens        : none;
}

/* Code blocks */
pre[class*=language-] {
    padding : var(--vspace-0_5) 0 var(--vspace-1);
    margin  : var(--vspace-1) 0;
    overflow: auto;
}

:not(pre)>code[class*=language-],
pre[class*=language-] {
    background: #272822;
}

/* Inline code */
:not(pre)>code[class*=language-] {
    padding    : 0.1em;
    white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: slategray;
}

.token.punctuation {
    color: #f8f8f2;
}

.token.namespace {
    opacity: 0.7;
}

.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
    color: #f92672;
}

.token.boolean,
.token.number {
    color: #ae81ff;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
    color: #a6e22e;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
    color: #f8f8f2;
}

.token.atrule,
.token.attr-value,
.token.function,
.token.class-name {
    color: #e6db74;
}

.token.keyword {
    color: #66d9ef;
}

.token.regex,
.token.important {
    color: #fd971f;
}

.token.important,
.token.bold {
    font-weight: bold;
}

.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}


/* ===================================================================
 * # Swiper 6.4.5
 *   Most modern mobile touch slider and framework with hardware accelerated transitions
 *   https://swiperjs.com
 *
 *   Copyright 2014-2020 Vladimir Kharlampidi
 *
 *   Released under the MIT License
 *
 *   Released on: December 18, 2020
 * ------------------------------------------------------------------- */
@font-face {
    font-family: "swiper-icons";
    src        : url("data:application/font-woff;charset=utf-8;base64, 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") format("woff");
    font-weight: 400;
    font-style : normal;
}

:root {
    --swiper-theme-color: #007aff;
}

.swiper-container {
    margin-left : auto;
    margin-right: auto;
    position    : relative;
    overflow    : hidden;
    list-style  : none;
    padding     : 0;
    /* Fix of Webkit flickering */
    z-index     : 1;
}

.swiper-container-vertical>.swiper-wrapper {
    flex-direction: column;
}

.swiper-wrapper {
    position           : relative;
    width              : 100%;
    height             : 100%;
    z-index            : 1;
    display            : flex;
    transition-property: transform;
    box-sizing         : content-box;
}

.swiper-container-android .swiper-slide,
.swiper-wrapper {
    transform: translate3d(0px, 0, 0);
}

.swiper-container-multirow>.swiper-wrapper {
    flex-wrap: wrap;
}

.swiper-container-multirow-column>.swiper-wrapper {
    flex-wrap     : wrap;
    flex-direction: column;
}

.swiper-container-free-mode>.swiper-wrapper {
    transition-timing-function: ease-out;
    margin                    : 0 auto;
}

.swiper-slide {
    flex-shrink        : 0;
    width              : 100%;
    height             : 100%;
    position           : relative;
    transition-property: transform;
}

.swiper-slide-invisible-blank {
    visibility: hidden;
}

/* Auto Height */
.swiper-container-autoheight,
.swiper-container-autoheight .swiper-slide {
    height: auto;
}

.swiper-container-autoheight .swiper-wrapper {
    align-items        : flex-start;
    transition-property: transform, height;
}

/* 3D Effects */
.swiper-container-3d {
    perspective: 1200px;
}

.swiper-container-3d .swiper-wrapper,
.swiper-container-3d .swiper-slide,
.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top,
.swiper-container-3d .swiper-slide-shadow-bottom,
.swiper-container-3d .swiper-cube-shadow {
    transform-style: preserve-3d;
}

.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top,
.swiper-container-3d .swiper-slide-shadow-bottom {
    position      : absolute;
    left          : 0;
    top           : 0;
    width         : 100%;
    height        : 100%;
    pointer-events: none;
    z-index       : 10;
}

.swiper-container-3d .swiper-slide-shadow-left {
    background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}

.swiper-container-3d .swiper-slide-shadow-right {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}

.swiper-container-3d .swiper-slide-shadow-top {
    background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}

.swiper-container-3d .swiper-slide-shadow-bottom {
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}

/* CSS Mode */
.swiper-container-css-mode>.swiper-wrapper {
    overflow          : auto;
    scrollbar-width   : none;
    /* For Firefox */
    -ms-overflow-style: none;
    /* For Internet Explorer and Edge */
}

.swiper-container-css-mode>.swiper-wrapper::-webkit-scrollbar {
    display: none;
}

.swiper-container-css-mode>.swiper-wrapper>.swiper-slide {
    scroll-snap-align: start start;
}

.swiper-container-horizontal.swiper-container-css-mode>.swiper-wrapper {
    scroll-snap-type: x mandatory;
}

.swiper-container-vertical.swiper-container-css-mode>.swiper-wrapper {
    scroll-snap-type: y mandatory;
}

:root {
    --swiper-navigation-size : 44px;
    /*
    --swiper-navigation-color: var(--swiper-theme-color);
    */
}

.swiper-button-prev,
.swiper-button-next {
    position       : absolute;
    top            : 50%;
    width          : calc(var(--swiper-navigation-size) / 44 * 27);
    height         : var(--swiper-navigation-size);
    margin-top     : calc(-1 * var(--swiper-navigation-size) / 2);
    z-index        : 10;
    cursor         : pointer;
    display        : flex;
    align-items    : center;
    justify-content: center;
    color          : var(--swiper-navigation-color, var(--swiper-theme-color));
}

.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
    opacity       : 0.35;
    cursor        : auto;
    pointer-events: none;
}

.swiper-button-prev:after,
.swiper-button-next:after {
    font-family   : swiper-icons;
    font-size     : var(--swiper-navigation-size);
    text-transform: none !important;
    letter-spacing: 0;
    text-transform: none;
    font-variant  : initial;
    line-height   : 1;
}

.swiper-button-prev,
.swiper-container-rtl .swiper-button-next {
    left : 10px;
    right: auto;
}

.swiper-button-prev:after,
.swiper-container-rtl .swiper-button-next:after {
    content: "prev";
}

.swiper-button-next,
.swiper-container-rtl .swiper-button-prev {
    right: 10px;
    left : auto;
}

.swiper-button-next:after,
.swiper-container-rtl .swiper-button-prev:after {
    content: "next";
}

.swiper-button-prev.swiper-button-white,
.swiper-button-next.swiper-button-white {
    --swiper-navigation-color: #ffffff;
}

.swiper-button-prev.swiper-button-black,
.swiper-button-next.swiper-button-black {
    --swiper-navigation-color: #000000;
}

.swiper-button-lock {
    display: none;
}

:root {
    /*
    --swiper-pagination-color: var(--swiper-theme-color);
    */
}

.swiper-pagination {
    position  : absolute;
    text-align: center;
    transition: 300ms opacity;
    transform : translate3d(0, 0, 0);
    z-index   : 10;
}

.swiper-pagination.swiper-pagination-hidden {
    opacity: 0;
}

/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-container-horizontal>.swiper-pagination-bullets {
    bottom: 10px;
    left  : 0;
    width : 100%;
}

/* Bullets */
.swiper-pagination-bullets-dynamic {
    overflow : hidden;
    font-size: 0;
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transform: scale(0.33);
    position : relative;
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
    transform: scale(1);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
    transform: scale(1);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
    transform: scale(0.66);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
    transform: scale(0.33);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
    transform: scale(0.66);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
    transform: scale(0.33);
}

.swiper-pagination-bullet {
    width        : 8px;
    height       : 8px;
    display      : inline-block;
    border-radius: 100%;
    background   : #000;
    opacity      : 0.2;
}

button.swiper-pagination-bullet {
    border            : none;
    margin            : 0;
    padding           : 0;
    box-shadow        : none;
    -webkit-appearance: none;
    -moz-appearance   : none;
    appearance        : none;
}

.swiper-pagination-clickable .swiper-pagination-bullet {
    cursor: pointer;
}

.swiper-pagination-bullet-active {
    opacity   : 1;
    background: var(--swiper-pagination-color, var(--swiper-theme-color));
}

.swiper-container-vertical>.swiper-pagination-bullets {
    right    : 10px;
    top      : 50%;
    transform: translate3d(0px, -50%, 0);
}

.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {
    margin : 6px 0;
    display: block;
}

.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    top      : 50%;
    transform: translateY(-50%);
    width    : 8px;
}

.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    display   : inline-block;
    transition: 200ms transform, 200ms top;
}

.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 4px;
}

.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    left       : 50%;
    transform  : translateX(-50%);
    white-space: nowrap;
}

.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: 200ms transform, 200ms left;
}

.swiper-container-horizontal.swiper-container-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    transition: 200ms transform, 200ms right;
}

/* Progress */
.swiper-pagination-progressbar {
    background: rgba(0, 0, 0, 0.25);
    position  : absolute;
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    background      : var(--swiper-pagination-color, var(--swiper-theme-color));
    position        : absolute;
    left            : 0;
    top             : 0;
    width           : 100%;
    height          : 100%;
    transform       : scale(0);
    transform-origin: left top;
}

.swiper-container-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    transform-origin: right top;
}

.swiper-container-horizontal>.swiper-pagination-progressbar,
.swiper-container-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
    width : 100%;
    height: 4px;
    left  : 0;
    top   : 0;
}

.swiper-container-vertical>.swiper-pagination-progressbar,
.swiper-container-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
    width : 4px;
    height: 100%;
    left  : 0;
    top   : 0;
}

.swiper-pagination-white {
    --swiper-pagination-color: #ffffff;
}

.swiper-pagination-black {
    --swiper-pagination-color: #000000;
}

.swiper-pagination-lock {
    display: none;
}

/* Scrollbar */
.swiper-scrollbar {
    border-radius   : 10px;
    position        : relative;
    -ms-touch-action: none;
    background      : rgba(0, 0, 0, 0.1);
}

.swiper-container-horizontal>.swiper-scrollbar {
    position: absolute;
    left    : 1%;
    bottom  : 3px;
    z-index : 50;
    height  : 5px;
    width   : 98%;
}

.swiper-container-vertical>.swiper-scrollbar {
    position: absolute;
    right   : 3px;
    top     : 1%;
    z-index : 50;
    width   : 5px;
    height  : 98%;
}

.swiper-scrollbar-drag {
    height       : 100%;
    width        : 100%;
    position     : relative;
    background   : rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    left         : 0;
    top          : 0;
}

.swiper-scrollbar-cursor-drag {
    cursor: move;
}

.swiper-scrollbar-lock {
    display: none;
}

.swiper-zoom-container {
    width          : 100%;
    height         : 100%;
    display        : flex;
    justify-content: center;
    align-items    : center;
    text-align     : center;
}

.swiper-zoom-container>img,
.swiper-zoom-container>svg,
.swiper-zoom-container>canvas {
    max-width    : 100%;
    max-height   : 100%;
    -o-object-fit: contain;
    object-fit   : contain;
}

.swiper-slide-zoomed {
    cursor: move;
}

/* Preloader */
:root {
    /*
    --swiper-preloader-color: var(--swiper-theme-color);
    */
}

.swiper-lazy-preloader {
    width           : 42px;
    height          : 42px;
    position        : absolute;
    left            : 50%;
    top             : 50%;
    margin-left     : -21px;
    margin-top      : -21px;
    z-index         : 10;
    transform-origin: 50%;
    animation       : swiper-preloader-spin 1s infinite linear;
    box-sizing      : border-box;
    border          : 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
    border-radius   : 50%;
    border-top-color: transparent;
}

.swiper-lazy-preloader-white {
    --swiper-preloader-color: #fff;
}

.swiper-lazy-preloader-black {
    --swiper-preloader-color: #000;
}

@keyframes swiper-preloader-spin {
    100% {
        transform: rotate(360deg);
    }
}

/* a11y */
.swiper-container .swiper-notification {
    position      : absolute;
    left          : 0;
    top           : 0;
    pointer-events: none;
    opacity       : 0;
    z-index       : -1000;
}

.swiper-container-fade.swiper-container-free-mode .swiper-slide {
    transition-timing-function: ease-out;
}

.swiper-container-fade .swiper-slide {
    pointer-events     : none;
    transition-property: opacity;
}

.swiper-container-fade .swiper-slide .swiper-slide {
    pointer-events: none;
}

.swiper-container-fade .swiper-slide-active,
.swiper-container-fade .swiper-slide-active .swiper-slide-active {
    pointer-events: auto;
}

.swiper-container-cube {
    overflow: visible;
}

.swiper-container-cube .swiper-slide {
    pointer-events     : none;
    backface-visibility: hidden;
    z-index            : 1;
    visibility         : hidden;
    transform-origin   : 0 0;
    width              : 100%;
    height             : 100%;
}

.swiper-container-cube .swiper-slide .swiper-slide {
    pointer-events: none;
}

.swiper-container-cube.swiper-container-rtl .swiper-slide {
    transform-origin: 100% 0;
}

.swiper-container-cube .swiper-slide-active,
.swiper-container-cube .swiper-slide-active .swiper-slide-active {
    pointer-events: auto;
}

.swiper-container-cube .swiper-slide-active,
.swiper-container-cube .swiper-slide-next,
.swiper-container-cube .swiper-slide-prev,
.swiper-container-cube .swiper-slide-next+.swiper-slide {
    pointer-events: auto;
    visibility    : visible;
}

.swiper-container-cube .swiper-slide-shadow-top,
.swiper-container-cube .swiper-slide-shadow-bottom,
.swiper-container-cube .swiper-slide-shadow-left,
.swiper-container-cube .swiper-slide-shadow-right {
    z-index            : 0;
    backface-visibility: hidden;
}

.swiper-container-cube .swiper-cube-shadow {
    position  : absolute;
    left      : 0;
    bottom    : 0px;
    width     : 100%;
    height    : 100%;
    background: #000;
    opacity   : 0.6;
    filter    : blur(50px);
    z-index   : 0;
}

.swiper-container-flip {
    overflow: visible;
}

.swiper-container-flip .swiper-slide {
    pointer-events     : none;
    backface-visibility: hidden;
    z-index            : 1;
}

.swiper-container-flip .swiper-slide .swiper-slide {
    pointer-events: none;
}

.swiper-container-flip .swiper-slide-active,
.swiper-container-flip .swiper-slide-active .swiper-slide-active {
    pointer-events: auto;
}

.swiper-container-flip .swiper-slide-shadow-top,
.swiper-container-flip .swiper-slide-shadow-bottom,
.swiper-container-flip .swiper-slide-shadow-left,
.swiper-container-flip .swiper-slide-shadow-right {
    z-index            : 0;
    backface-visibility: hidden;
}

/* ==========================================================================
 * # GLightbox
 *
 * GLightbox 3.2.0
 * https://biati-digital.github.io/glightbox/
 * -------------------------------------------------------------------------- 
 */
.glightbox-container {
    width                   : 100%;
    height                  : 100%;
    position                : fixed;
    top                     : 0;
    left                    : 0;
    z-index                 : 999999 !important;
    overflow                : hidden;
    touch-action            : none;
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust   : 100%;
    text-size-adjust        : 100%;
    backface-visibility     : hidden;
    outline                 : none;
}

.glightbox-container.inactive {
    display: none;
}

.glightbox-container .gcontainer {
    position: relative;
    width   : 100%;
    height  : 100%;
    z-index : 9999;
    overflow: hidden;
}

.glightbox-container .gslider {
    transition     : transform 0.4s ease;
    height         : 100%;
    left           : 0;
    top            : 0;
    width          : 100%;
    position       : relative;
    overflow       : hidden;
    display        : flex !important;
    justify-content: center;
    align-items    : center;
    transform      : translate3d(0, 0, 0);
}

.glightbox-container .gslide {
    width              : 100%;
    position           : absolute;
    -webkit-user-select: none;
    -moz-user-select   : none;
    user-select        : none;
    display            : flex;
    align-items        : center;
    justify-content    : center;
    opacity            : 0;
}

.glightbox-container .gslide.current {
    opacity : 1;
    z-index : 99999;
    position: relative;
}

.glightbox-container .gslide.prev {
    opacity: 1;
    z-index: 9999;
}

.glightbox-container .gslide-inner-content {
    width: 100%;
}

.glightbox-container .ginner-container {
    position       : relative;
    width          : 100%;
    display        : flex;
    justify-content: center;
    flex-direction : column;
    max-width      : 100%;
    margin         : auto;
    height         : 100vh;
}

.glightbox-container .ginner-container.gvideo-container {
    width: 100%;
}

.glightbox-container .ginner-container.desc-bottom,
.glightbox-container .ginner-container.desc-top {
    flex-direction: column;
}

.glightbox-container .ginner-container.desc-left,
.glightbox-container .ginner-container.desc-right {
    max-width: 100% !important;
}

.gslide iframe,
.gslide video {
    outline                   : none !important;
    border                    : none;
    min-height                : 165px;
    -webkit-overflow-scrolling: touch;
    touch-action              : auto;
}

.gslide:not(.current) {
    pointer-events: none;
}

.gslide-image {
    align-items: center;
}

.gslide-image img {
    max-height         : 100vh;
    display            : block;
    padding            : 0;
    float              : none;
    outline            : none;
    border             : none;
    -webkit-user-select: none;
    -moz-user-select   : none;
    user-select        : none;
    max-width          : 100vw;
    width              : auto;
    height             : auto;
    -o-object-fit      : cover;
    object-fit         : cover;
    touch-action       : none;
    margin             : auto;
    min-width          : 200px;
}

.desc-top .gslide-image img,
.desc-bottom .gslide-image img {
    width: auto;
}

.desc-left .gslide-image img,
.desc-right .gslide-image img {
    width    : auto;
    max-width: 100%;
}

.gslide-image img.zoomable {
    position: relative;
}

.gslide-image img.dragging {
    cursor    : grabbing !important;
    transition: none;
}

.gslide-video {
    position : relative;
    max-width: 100vh;
    width    : 100% !important;
}

.gslide-video .plyr__poster-enabled.plyr--loading .plyr__poster {
    display: none;
}

.gslide-video .gvideo-wrapper {
    width       : 100%;
    /* max-width: 160vmin; */
    margin      : auto;
}

.gslide-video::before {
    content   : "";
    position  : absolute;
    width     : 100%;
    height    : 100%;
    background: rgba(255, 0, 0, 0.34);
    display   : none;
}

.gslide-video.playing::before {
    display: none;
}

.gslide-video.fullscreen {
    max-width: 100% !important;
    min-width: 100%;
    height   : 75vh;
}

.gslide-video.fullscreen video {
    max-width: 100% !important;
    width    : 100% !important;
}

.gslide-inline {
    background: #fff;
    text-align: left;
    max-height: calc(100vh - 40px);
    overflow  : auto;
    max-width : 100%;
    margin    : auto;
}

.gslide-inline .ginlined-content {
    padding: 20px;
    width  : 100%;
}

.gslide-inline .dragging {
    cursor    : grabbing !important;
    transition: none;
}

.ginlined-content {
    overflow: auto;
    display : block !important;
    opacity : 1;
}

.gslide-external {
    display   : flex;
    width     : 100%;
    min-width : 100%;
    background: #fff;
    padding   : 0;
    overflow  : auto;
    max-height: 75vh;
    height    : 100%;
}

.gslide-media {
    display: flex;
    width  : auto;
}

.zoomed .gslide-media {
    box-shadow: none !important;
}

.desc-top .gslide-media,
.desc-bottom .gslide-media {
    margin        : 0 auto;
    flex-direction: column;
}

.gslide-description {
    position: relative;
    flex    : 1 0 100%;
}

.gslide-description.description-left,
.gslide-description.description-right {
    max-width: 100%;
}

.gslide-description.description-bottom,
.gslide-description.description-top {
    margin: 0 auto;
    width : 100%;
}

.gslide-description p {
    margin-bottom: 12px;
}

.gslide-description p:last-child {
    margin-bottom: 0;
}

.zoomed .gslide-description {
    display: none;
}

.glightbox-button-hidden {
    display: none;
}

/*
 * Description for mobiles
 * something like facebook does the description
 * for the photos
*/
.glightbox-mobile .glightbox-container .gslide-description {
    height        : auto !important;
    width         : 100%;
    position      : absolute;
    bottom        : 0;
    padding       : 19px 11px;
    max-width     : 100vw !important;
    order         : 2 !important;
    max-height    : 78vh;
    overflow      : auto !important;
    background    : linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.75) 100%);
    transition    : opacity 0.3s linear;
    padding-bottom: 50px;
}

.glightbox-mobile .glightbox-container .gslide-title {
    color    : #fff;
    font-size: 1em;
}

.glightbox-mobile .glightbox-container .gslide-desc {
    color: #a1a1a1;
}

.glightbox-mobile .glightbox-container .gslide-desc a {
    color      : #fff;
    font-weight: bold;
}

.glightbox-mobile .glightbox-container .gslide-desc * {
    color: inherit;
}

.glightbox-mobile .glightbox-container .gslide-desc .desc-more {
    color  : #fff;
    opacity: 0.4;
}

.gdesc-open .gslide-media {
    transition: opacity 0.5s ease;
    opacity   : 0.4;
}

.gdesc-open .gdesc-inner {
    padding-bottom: 30px;
}

.gdesc-closed .gslide-media {
    transition: opacity 0.5s ease;
    opacity   : 1;
}

.greset {
    transition: all 0.3s ease;
}

.gabsolute {
    position: absolute;
}

.grelative {
    position: relative;
}

.glightbox-desc {
    display: none !important;
}

.glightbox-open {
    overflow: hidden;
}

.gloader {
    height            : 25px;
    width             : 25px;
    animation         : lightboxLoader 0.8s infinite linear;
    border            : 2px solid #fff;
    border-right-color: transparent;
    border-radius     : 50%;
    position          : absolute;
    display           : block;
    z-index           : 9999;
    left              : 0;
    right             : 0;
    margin            : 0 auto;
    top               : 47%;
}

.goverlay {
    width      : 100%;
    height     : calc(100vh + 1px);
    position   : fixed;
    top        : -1px;
    left       : 0;
    background : #000;
    will-change: opacity;
}

.glightbox-mobile .goverlay {
    background: #000;
}

.gprev,
.gnext,
.gclose {
    z-index        : 99999;
    cursor         : pointer;
    width          : 26px;
    height         : 44px;
    border         : none;
    display        : flex;
    justify-content: center;
    align-items    : center;
    flex-direction : column;
}

.gprev svg,
.gnext svg,
.gclose svg {
    display: block;
    width  : 25px;
    height : auto;
    margin : 0;
    padding: 0;
}

.gprev.disabled,
.gnext.disabled,
.gclose.disabled {
    opacity: 0.2;
}

.gprev .garrow,
.gnext .garrow,
.gclose .garrow {
    stroke: #fff;
}

.gbtn.focused {
    outline: 2px solid #0f3d81;
}

iframe.wait-autoplay {
    opacity: 0;
}

.glightbox-closing .gnext,
.glightbox-closing .gprev,
.glightbox-closing .gclose {
    opacity: 0 !important;
}

/*Skin */
.glightbox-clean .gslide-description {
    background: #fff;
}

.glightbox-clean .gdesc-inner {
    padding: 22px 20px;
}

.glightbox-clean .gslide-title {
    font-size    : 1em;
    font-weight  : normal;
    font-family  : arial;
    color        : #000;
    margin-bottom: 19px;
    line-height  : 1.4em;
}

.glightbox-clean .gslide-desc {
    font-size    : 0.86em;
    margin-bottom: 0;
    font-family  : arial;
    line-height  : 1.4em;
}

.glightbox-clean .gslide-video {
    background: #000;
}

.glightbox-clean .gprev,
.glightbox-clean .gnext,
.glightbox-clean .gclose {
    background-color: rgba(0, 0, 0, 0.75);
    border-radius   : 4px;
}

.glightbox-clean .gprev path,
.glightbox-clean .gnext path,
.glightbox-clean .gclose path {
    fill: #fff;
}

.glightbox-clean .gprev {
    position: absolute;
    top     : -100%;
    left    : 30px;
    width   : 40px;
    height  : 50px;
}

.glightbox-clean .gnext {
    position: absolute;
    top     : -100%;
    right   : 30px;
    width   : 40px;
    height  : 50px;
}

.glightbox-clean .gclose {
    width   : 35px;
    height  : 35px;
    top     : 15px;
    right   : 10px;
    position: absolute;
}

.glightbox-clean .gclose svg {
    width : 18px;
    height: auto;
}

.glightbox-clean .gclose:hover {
    opacity: 1;
}

/*CSS Animations*/
.gfadeIn {
    animation: gfadeIn 0.5s ease;
}

.gfadeOut {
    animation: gfadeOut 0.5s ease;
}

.gslideOutLeft {
    animation: gslideOutLeft 0.3s ease;
}

.gslideInLeft {
    animation: gslideInLeft 0.3s ease;
}

.gslideOutRight {
    animation: gslideOutRight 0.3s ease;
}

.gslideInRight {
    animation: gslideInRight 0.3s ease;
}

.gzoomIn {
    animation: gzoomIn 0.5s ease;
}

.gzoomOut {
    animation: gzoomOut 0.5s ease;
}

@keyframes lightboxLoader {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes gfadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes gfadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

@keyframes gslideInLeft {
    from {
        opacity  : 0;
        transform: translate3d(-60%, 0, 0);
    }

    to {
        visibility: visible;
        transform : translate3d(0, 0, 0);
        opacity   : 1;
    }
}

@keyframes gslideOutLeft {
    from {
        opacity   : 1;
        visibility: visible;
        transform : translate3d(0, 0, 0);
    }

    to {
        transform : translate3d(-60%, 0, 0);
        opacity   : 0;
        visibility: hidden;
    }
}

@keyframes gslideInRight {
    from {
        opacity   : 0;
        visibility: visible;
        transform : translate3d(60%, 0, 0);
    }

    to {
        transform: translate3d(0, 0, 0);
        opacity  : 1;
    }
}

@keyframes gslideOutRight {
    from {
        opacity   : 1;
        visibility: visible;
        transform : translate3d(0, 0, 0);
    }

    to {
        transform: translate3d(60%, 0, 0);
        opacity  : 0;
    }
}

@keyframes gzoomIn {
    from {
        opacity  : 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }

    to {
        opacity: 1;
    }
}

@keyframes gzoomOut {
    from {
        opacity: 1;
    }

    50% {
        opacity  : 0;
        transform: scale3d(0.3, 0.3, 0.3);
    }

    to {
        opacity: 0;
    }
}

@media (min-width: 769px) {
    .glightbox-container .ginner-container {
        width         : auto;
        height        : auto;
        flex-direction: row;
    }

    .glightbox-container .ginner-container.desc-top .gslide-description {
        order: 0;
    }

    .glightbox-container .ginner-container.desc-top .gslide-image,
    .glightbox-container .ginner-container.desc-top .gslide-image img {
        order: 1;
    }

    .glightbox-container .ginner-container.desc-left .gslide-description {
        order: 0;
    }

    .glightbox-container .ginner-container.desc-left .gslide-image {
        order: 1;
    }

    .gslide-image img {
        max-height: 97vh;
        max-width : 100%;
    }

    .gslide-image img.zoomable {
        cursor: zoom-in;
    }

    .zoomed .gslide-image img.zoomable {
        cursor: grab;
    }

    .gslide-inline {
        max-height: 95vh;
    }

    .gslide-external {
        max-height: 100vh;
    }

    .gslide-description.description-left,
    .gslide-description.description-right {
        max-width: 275px;
    }

    .glightbox-open {
        height: auto;
    }

    .goverlay {
        background: rgba(0, 0, 0, 0.92);
    }

    .glightbox-clean .gslide-media {
        box-shadow: 1px 2px 9px 0px rgba(0, 0, 0, 0.65);
    }

    .glightbox-clean .description-left .gdesc-inner,
    .glightbox-clean .description-right .gdesc-inner {
        position  : absolute;
        height    : 100%;
        overflow-y: auto;
    }

    .glightbox-clean .gprev,
    .glightbox-clean .gnext,
    .glightbox-clean .gclose {
        background-color: rgba(0, 0, 0, 0.32);
    }

    .glightbox-clean .gprev:hover,
    .glightbox-clean .gnext:hover,
    .glightbox-clean .gclose:hover {
        background-color: rgba(0, 0, 0, 0.7);
    }

    .glightbox-clean .gprev {
        top: 45%;
    }

    .glightbox-clean .gnext {
        top: 45%;
    }
}

@media (min-width: 992px) {
    .glightbox-clean .gclose {
        right: 24px;
    }
}

@media screen and (max-height: 420px) {
    .goverlay {
        background: #000;
    }
}